import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import Constants from 'expo-constants';
import api from './api';
import { getDeviceLanguage } from './i18nService';
import * as Sentry from '@sentry/react-native';

// Types
export interface WhatsNewLog {
  id: string;
  type: 'dark' | 'red' | 'cyan' | 'purple';
  date: string;
  title_en: string;
  title_es: string;
  title_dom: string;
  description_en: string;
  description_es: string;
  description_dom: string;
  videoUrl?: string;
  appVersion: string;
  platform: 'ios' | 'android' | 'both';
  createdAt: string;
  updatedAt: string;
  updateApp?: boolean; // Added for mobile app logic
  // Computed fields for mobile app
  title?: string; // Localized title based on user's language
  description?: string; // Localized description based on user's language
}

// Storage keys
const WHATSNEW_STORAGE_KEY = 'taptrap_whatsnew_logs';
const WHATSNEW_LAST_VISIT_KEY = 'taptrap_whatsnew_last_visit';
const WHATSNEW_LAST_REFRESH_KEY = 'taptrap_whatsnew_last_refresh'; // Key for storing the timestamp of the last refresh

/**
 * Get current app language from AsyncStorage or fallback to device language
 */
const getCurrentAppLanguage = async (): Promise<'en' | 'es' | 'dom'> => {
  try {
    const storedLanguage = await AsyncStorage.getItem('appLanguage');
    if (storedLanguage && ['en', 'es', 'dom'].includes(storedLanguage)) {
      return storedLanguage as 'en' | 'es' | 'dom';
    }
    // Fall back to device language
    return getDeviceLanguage();
  } catch (error) {
    console.error('Error getting current app language:', error);
    Sentry.captureException(error);
    return 'en'; // Default to English
  }
};

/**
 * Get localized text based on user's language preference
 */
const getLocalizedText = async (textEn: string, textEs: string, textDom: string): Promise<string> => {
  const language = await getCurrentAppLanguage();
  if (language === 'es') return textEs;
  if (language === 'dom') return textDom;
  return textEn; // Default to English
};

/**
 * Process logs to add localized title and description
 */
const processLogsForMobile = async (logs: WhatsNewLog[]): Promise<WhatsNewLog[]> => {
  const processedLogs = await Promise.all(logs.map(async log => ({
    ...log,
    title: await getLocalizedText(log.title_en, log.title_es, log.title_dom),
    description: await getLocalizedText(log.description_en, log.description_es, log.description_dom)
  })));
  return processedLogs;
};

/**
 * Get current app version from Expo config
 */
const getCurrentAppVersion = (): string => {
  return Constants.expoConfig?.version || '1.0.0';
};

/**
 * Compare two semantic versions
 * Returns true if currentVersion is less than requiredVersion
 */
const isVersionLower = (currentVersion: string, requiredVersion: string): boolean => {
  const parseVersion = (version: string) => {
    return version.split('.').map(num => parseInt(num, 10));
  };

  const current = parseVersion(currentVersion);
  const required = parseVersion(requiredVersion);

  for (let i = 0; i < Math.max(current.length, required.length); i++) {
    const currentPart = current[i] || 0;
    const requiredPart = required[i] || 0;

    if (currentPart < requiredPart) return true;
    if (currentPart > requiredPart) return false;
  }

  return false; // Versions are equal
};

/**
 * Helper function to get the timestamp of the last What's New refresh
 */
const getLastWhatsNewRefreshTime = async (): Promise<number> => {
  try {
    const storedTimestamp = await AsyncStorage.getItem(WHATSNEW_LAST_REFRESH_KEY);
    if (!storedTimestamp) {
      return 0; // If no timestamp is stored, return 0 to force refresh
    }

    return parseInt(storedTimestamp, 10);
  } catch (error) {
    console.error('Error getting last What\'s New refresh time:', error);
    Sentry.captureException(error);
    return 0; // On error, return 0 to force refresh
  }
};

/**
 * Helper function to update the timestamp of the last What's New refresh
 */
const updateLastWhatsNewRefreshTime = async (): Promise<boolean> => {
  try {
    const now = Date.now();
    await AsyncStorage.setItem(WHATSNEW_LAST_REFRESH_KEY, now.toString());
    return true;
  } catch (error) {
    console.error('Error updating last What\'s New refresh time:', error);
    Sentry.captureException(error);
    return false;
  }
};

/**
 * Save What's New logs to storage
 */
const saveLogsToStorage = async (logs: WhatsNewLog[]): Promise<void> => {
  try {
    const dataToStore = JSON.stringify(logs);
    await AsyncStorage.setItem(WHATSNEW_STORAGE_KEY, dataToStore);
    console.log('What\'s New logs saved to storage');
  } catch (error) {
    console.error('Error saving What\'s New logs to storage:', error);
    Sentry.captureException(error);
  }
};

/**
 * Get cached What's New logs from storage
 */
const getCachedLogs = async (): Promise<WhatsNewLog[]> => {
  try {
    const storedData = await AsyncStorage.getItem(WHATSNEW_STORAGE_KEY);
    if (!storedData) return [];

    const logs = JSON.parse(storedData) as WhatsNewLog[];
    console.log(`Retrieved ${logs.length} cached What's New logs`);
    return logs;
  } catch (error) {
    console.error('Error retrieving cached What\'s New logs:', error);
    Sentry.captureException(error);
    return [];
  }
};

/**
 * Fetch What's New logs from API
 */
const fetchLogsFromAPI = async (): Promise<WhatsNewLog[] | null> => {
  try {
    console.log('Fetching What\'s New logs from API...');
    const response = await api.get('/whatsnew');

    if (response.data && Array.isArray(response.data)) {
      console.log(`Fetched ${response.data.length} What's New logs from API`);
      return response.data;
    }

    console.warn('Invalid API response format for What\'s New logs');
    return null;
  } catch (error) {
    console.error('Error fetching What\'s New logs from API:', error);
    Sentry.captureException(error);
    return null;
  }
};

/**
 * Check internet connectivity
 */
const isInternetAvailable = async (): Promise<boolean> => {
  try {
    const networkState = await NetInfo.fetch();
    return networkState.isConnected === true;
  } catch (error) {
    console.error('Error checking internet connectivity:', error);
    Sentry.captureException(error);
    return false;
  }
};

/**
 * Main function to fetch and update What's New logs
 * Similar pattern to fetchAndUpdateGameContent from contentService
 */
export const fetchAndUpdateWhatsNewLogs = async (forceRefresh: boolean = false): Promise<WhatsNewLog[]> => {
  try {
    console.log('🆕 Starting What\'s New logs fetch process...');

    // Check if we've refreshed What's New recently (within the last 60 minutes)
    const now = Date.now();
    const lastRefreshTime = await getLastWhatsNewRefreshTime();
    const timeSinceLastRefresh = now - lastRefreshTime;

    if (timeSinceLastRefresh < 3600000 && !forceRefresh) { // 60 min threshold
      console.log('⏱️ WHATSNEW: Skipping What\'s New refresh (last refresh was',
                 Math.round(timeSinceLastRefresh / 1000), 'seconds ago)');

      // Return cached logs without making a new API request
      const cachedLogs = await getCachedLogs();
      if (cachedLogs && cachedLogs.length > 0) {
        console.log(`📦 Using cached What's New logs: ${cachedLogs.length} logs`);
        return await processLogsForMobile(cachedLogs);
      } else {
        // If no cached logs available, force refresh
        console.log('⚠️ No cached What\'s New logs available, forcing refresh');
        forceRefresh = true;
      }
    } else if (forceRefresh) {
      console.log('🔄 Force refresh requested, bypassing time threshold check');
    } else {
      console.log('⏱️ Time threshold passed (', Math.round(timeSinceLastRefresh / 1000),
                 'seconds since last refresh), proceeding with What\'s New update');
    }

    // Check if we have internet connection
    const isOnline = await isInternetAvailable();

    if (!isOnline) {
      console.log('📱 No internet connection, using cached What\'s New logs');
      const cachedLogs = await getCachedLogs();
      return await processLogsForMobile(cachedLogs);
    }

    // Try to fetch fresh logs from API
    const freshLogs = await fetchLogsFromAPI();

    // Check if API call was successful (freshLogs will be an array, even if empty, or null on error)
    if (freshLogs !== null) {
      console.log('✅ Fresh What\'s New logs fetched successfully');

      // Process logs for mobile app (add localized text and updateApp flag)
      const currentVersion = getCurrentAppVersion();
      const localizedLogs = await processLogsForMobile(freshLogs);
      const processedLogs = localizedLogs.map(log => ({
        ...log,
        updateApp: isVersionLower(currentVersion, log.appVersion)
      }));

      // Save to cache (even if empty array)
      await saveLogsToStorage(processedLogs);

      return processedLogs;
    } else {
      console.log('❌ Failed to fetch fresh What\'s New logs, falling back to cache');
      const cachedLogs = await getCachedLogs();
      return await processLogsForMobile(cachedLogs);
    }
  } catch (error) {
    console.error('Error in fetchAndUpdateWhatsNewLogs:', error);
    Sentry.captureException(error);
    const cachedLogs = await getCachedLogs();
    return await processLogsForMobile(cachedLogs);
  }
};

/**
 * Check for new logs since last visit (for home screen dot indicator)
 */
export const checkForNewLogs = async (): Promise<{ hasNewLogs: boolean; newLogsCount: number }> => {
  try {
    // Check if we have internet connection
    const isOnline = await isInternetAvailable();
    
    if (!isOnline) {
      console.log('📱 No internet connection, cannot check for new logs');
      return { hasNewLogs: false, newLogsCount: 0 };
    }

    // Get last visit timestamp
    const lastVisit = await AsyncStorage.getItem(WHATSNEW_LAST_VISIT_KEY);
    
    console.log('🔍 Checking for new What\'s New logs since:', lastVisit || 'never');
    
    // Make lightweight API call
    const response = await api.get('/whatsnew/check-new', {
      params: lastVisit ? { lastVisit } : {}
    });
    
    const { hasNewLogs, newLogsCount } = response.data;
    
    console.log(`Found ${newLogsCount} new What's New logs`);
    
    return { hasNewLogs, newLogsCount };
  } catch (error) {
    console.error('Error checking for new What\'s New logs:', error);
    Sentry.captureException(error);
    return { hasNewLogs: false, newLogsCount: 0 };
  }
};

/**
 * Mark What's New as visited (removes red dot indicator)
 */
export const markWhatsNewAsVisited = async (): Promise<void> => {
  try {
    const now = new Date().toISOString();
    await AsyncStorage.setItem(WHATSNEW_LAST_VISIT_KEY, now);
    console.log('✅ What\'s New marked as visited:', now);
  } catch (error) {
    console.error('Error marking What\'s New as visited:', error);
    Sentry.captureException(error);
  }
};

/**
 * Get cached logs without making API calls (for immediate display)
 */
export const getCachedWhatsNewLogs = async (): Promise<WhatsNewLog[]> => {
  const cachedLogs = await getCachedLogs();
  // Process cached logs to ensure they have localized text
  return await processLogsForMobile(cachedLogs);
};
